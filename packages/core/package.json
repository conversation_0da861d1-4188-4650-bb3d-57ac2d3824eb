{"name": "@drauu/core", "version": "0.4.3", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/drauu#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/drauu.git"}, "bugs": {"url": "https://github.com/antfu/drauu/issues"}, "keywords": [], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "main": "dist/index.js", "module": "dist/index.mjs", "unpkg": "dist/index.global.js", "jsdelivr": "dist/index.global.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "nr build --watch", "build": "tsup src/index.ts --format esm,cjs,iife --dts --no-splitting --clean --dts-resolve", "prepublishOnly": "nr build"}, "devDependencies": {"perfect-freehand": "^1.2.2"}}