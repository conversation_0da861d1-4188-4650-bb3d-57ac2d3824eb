{"name": "drauu", "version": "0.4.3", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/drauu#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/drauu.git"}, "bugs": {"url": "https://github.com/antfu/drauu/issues"}, "keywords": [], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "main": "dist/index.js", "module": "dist/index.mjs", "unpkg": "dist/index.global.js", "jsdelivr": "dist/index.global.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "nr tsup --watch", "tsup": "tsup src/index.ts --format esm,cjs --dts --no-splitting --clean", "build": "nr tsup && cp ../core/dist/index.global.js dist", "prepublishOnly": "nr build"}, "dependencies": {"@drauu/core": "workspace:*"}}