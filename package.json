{"type": "module", "version": "0.4.3", "private": true, "packageManager": "pnpm@10.4.1", "scripts": {"play:dev": "pnpm -C playground run dev --host --open", "play:build": "pnpm -C playground run build", "js:dev": "pnpm run -r --parallel --filter='./packages/*' dev", "js:build": "pnpm run -r --filter='./packages/*' build", "dev": "nr play:dev", "build": "nr js:build", "lint": "eslint .", "release": "bumpp package.json 'packages/*/package.json' && pnpm -r publish --access public"}, "devDependencies": {"@antfu/eslint-config": "^4.3.0", "@antfu/ni": "^23.3.1", "@types/node": "^22.13.5", "bumpp": "^10.0.3", "eslint": "^9.21.0", "nanoevents": "^9.1.0", "pnpm": "^10.4.1", "tsup": "^8.3.6", "typescript": "^5.7.3"}}