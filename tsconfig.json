{"compilerOptions": {"incremental": false, "target": "es2016", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"drauu": ["./packages/core/src/index.ts"]}, "resolveJsonModule": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "exclude": ["**/dist", "node_modules"]}